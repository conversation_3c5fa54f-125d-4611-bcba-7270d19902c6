@echo off
cd /d "C:\Users\<USER>\Desktop\LiveTalking\LiveTalkingzhanli"
echo Current directory: %CD%
echo.

echo Checking if app.py exists...
if exist app.py (
    echo app.py found
) else (
    echo ERROR: app.py not found in current directory
    dir *.py
    pause
    exit
)

echo Initializing conda...
call conda init cmd.exe
call conda activate nerfstreamm
if %errorlevel% neq 0 (
    echo Failed to activate conda environment nerfstreamm
    echo Trying alternative activation method...
    call C:\Users\<USER>\anaconda3\Scripts\activate.bat nerfstreamm
)

echo Environment setup complete, starting Python app...
echo Browser will open in 8 seconds...
start /b timeout /t 8 /nobreak && start "" "http://127.0.0.1:8010/dashboard.html"

python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "C:\Users\<USER>\Desktop\GPT-SoVITS\GPT_SoVITS\123.wav" --REF_TEXT "毕竟第一次对话时我确实把公司背景业度又能增加亲和力。特意在结尾再次强调免费特性。" --customvideo_config data/custom_config.json

echo Application ended
pause
