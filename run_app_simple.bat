@echo off
cd /d "%~dp0"
call conda activate nerfstreamm

echo Starting application...
start /b python app.py --transport webrtc --model wav2lip --avatar_id wav2lip256_avatar1 --tts gpt-sovits --TTS_SERVER http://127.0.0.1:9880 --REF_FILE "C:\Users\<USER>\Desktop\GPT-SoVITS\GPT_SoVITS\123.wav" --REF_TEXT "毕竟第一次对话时我确实把公司背景业度又能增加亲和力。特意在结尾再次强调免费特性。" --customvideo_config data/custom_config.json

echo Waiting for server to start...
timeout /t 5 /nobreak >nul

echo Opening browser...
start http://127.0.0.1:8010/dashboard.html

echo Server is running. Press Ctrl+C to stop.
echo Browser should open automatically at http://127.0.0.1:8010/dashboard.html
cmd /k
