import subprocess
import sys

# 定义命令参数
cmd = [
    "python", "app.py",
    "--transport", "webrtc",
    "--model", "wav2lip", 
    "--avatar_id", "wav2lip256_avatar1",
    "--tts", "gpt-sovits",
    "--TTS_SERVER", "http://127.0.0.1:9880",
    "--REF_FILE", "C:\\Users\\<USER>\\Desktop\\GPT-SoVITS\\GPT_SoVITS\\123.wav",
    "--REF_TEXT", "毕竟第一次对话时我确实把公司背景业度又能增加亲和力。特意在结尾再次强调免费特性。",
    "--customvideo_config", "data/custom_config.json"
]

# 运行命令
subprocess.run(cmd)
